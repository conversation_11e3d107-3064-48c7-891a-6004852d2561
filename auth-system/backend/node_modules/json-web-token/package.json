{"name": "json-web-token", "version": "3.2.0", "description": "JSON Web Token (JWT) is a compact token format intended for space constrained environments such as HTTP Authorization headers and URI query parameters.", "main": "index.js", "types": "index.d.ts", "scripts": {"coverage:open": "open coverage/index.html", "coverage": "nyc report --reporter=text-lcov | coveralls", "coverage:check": "nyc check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test": "standard --fix && nyc --reporter=html --reporter=text mocha", "bench": "echo 'installing dependencies first ...' && sleep 1 && npm i --save-dev benchmark microtime && echo '' && node bench && npm uninstall --save-dev benchmark microtime"}, "files": ["LICENSE", "README.md", "index.js", "index.d.ts"], "repository": {"type": "git", "url": "git://github.com/joaquimserafim/json-web-token.git"}, "keywords": ["jwt", "json-web-token", "web", "http", "tokens", "authorization"], "author": "@joaquimserafim", "license": "ISC", "bugs": {"url": "https://github.com/joaquimserafim/json-web-token/issues"}, "homepage": "https://github.com/joaquimserafim/json-web-token", "dependencies": {"base64-url": "^2.3.2", "is.object": "^1.0.0", "json-parse-safe": "^2.0.0", "xtend": "^4.0.2"}, "devDependencies": {"coveralls": "^3.0.6", "istanbul": "0.4.5", "mocha": "^6.2.1", "nyc": "^14.1.1", "pre-commit": "^1.2.2", "standard": "^14.3.1"}, "nyc": {"include": ["index.js"]}, "pre-commit": ["test", "coverage"], "engine": {"node": ">=8"}}