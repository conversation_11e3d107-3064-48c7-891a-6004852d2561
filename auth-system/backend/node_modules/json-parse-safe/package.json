{"name": "json-parse-safe", "version": "2.0.0", "description": "Parse your json safely and stop writing try {} catch {}", "main": "index.js", "scripts": {"coverage:open": "open coverage/index.html", "coverage:check": "nyc check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "coverage": "nyc report --reporter=text-lcov | coveralls", "test": "standard --fix && nyc --reporter=html --reporter=text tape test.js"}, "files": ["LICENSE", "README.md", "index.js"], "repository": {"type": "git", "url": "https://github.com/joaquimserafim/json-parse-safe.git"}, "keywords": ["json", "parse", "safe", "try/catch"], "author": "@joaquimserafim", "license": "ISC", "bugs": {"url": "https://github.com/joaquimserafim/json-parse-safe/issues"}, "homepage": "https://github.com/joaquimserafim/json-parse-safe", "devDependencies": {"coveralls": "^3.0.4", "nyc": "^14.1.1", "pre-commit": "^1.0.2", "standard": "^12.0.1", "tape": "^4.10.2"}, "pre-commit": ["test", "coverage:check"], "engines": {"node": ">=6"}}