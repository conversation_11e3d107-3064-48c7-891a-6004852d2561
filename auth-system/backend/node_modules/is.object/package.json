{"name": "is.object", "version": "1.0.0", "description": "verifies if is an object and not an array or null", "main": "index.js", "files": ["LICENSE", "README.md", "index.js"], "scripts": {"coverage": "open coverage/lcov-report/index.html", "coverage:check": "istanbul check-coverage --statements 100 --functions 100 --lines 100 --branches 100", "test": "standard --fix && istanbul cover -x test.js tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/joaquimserafim/is.object.git"}, "keywords": ["utils", "object", "isobject", "is.object", "type"], "author": "@JoaquimSerafim", "license": "ISC", "bugs": {"url": "https://github.com/joaquimserafim/is.object/issues"}, "homepage": "https://github.com/joaquimserafim/is.object#readme", "devDependencies": {"istanbul": "0.4.5", "pre-commit": "^1.2.2", "standard": "^8.6.0", "tape": "^4.6.3"}, "engines": {"node": ">=6.1"}, "pre-commit": ["test", "coverage:check"], "standard": {"ignore": ["bench.js"]}}